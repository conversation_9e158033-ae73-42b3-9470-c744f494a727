"""
Domain Classification Agent

This agent analyzes user input to extract requirements, classify domains,
and determine project complexity and constraints.
"""

import json
import logging
import re
from typing import Dict, Any, Tuple, List
from ..base_agent import BaseIgnitionAgent
from ..services.shared_memory import PlanGenerationState


class DomainClassificationAgent(BaseIgnitionAgent):
    """
    Agent responsible for domain analysis and requirement extraction.

    Capabilities:
    - Classify primary and sub-domains
    - Extract functional and non-functional requirements
    - Determine complexity level and constraints
    - Calculate confidence scores
    - AI-enhanced analysis for deeper insights
    """

    def __init__(self, config: Dict[str, Any] = None):
        """Initialize Domain Classification Agent."""
        super().__init__("domain_classifier", config)

        # Agent-specific configuration
        self.confidence_threshold = self.config.get('confidence_threshold', 0.7)

        # Load models from environment variables
        import os
        self.primary_model = os.getenv('GOOGLE_AI_DEFAULT_MODEL', 'gemini-2.0-flash')
        self.fallback_model = os.getenv('GOOGLE_AI_FALLBACK_MODEL', 'gemini-2.5-flash')

        self.temperature = self.config.get('temperature', 0.1)
        self.max_tokens = self.config.get('max_tokens', 1500)
        self.ai_timeout = self.config.get('ai_timeout', 30)

        # Model alternating counter for load balancing
        self.request_counter = 0
        self.last_model_used = None

        # Load domain patterns and configurations
        self.domain_patterns = self._load_domain_patterns()
        self.requirement_patterns = self._load_requirement_patterns()

        # Initialize direct Gemini client
        self._init_gemini_client()

    def _init_gemini_client(self):
        """Initialize direct Gemini client"""
        try:
            import google.generativeai as genai
            import os

            # Get API key from environment
            api_key = os.getenv('GOOGLE_AI_API_KEY') or os.getenv('GEMINI_API_KEY')
            if not api_key:
                raise ValueError("GOOGLE_AI_API_KEY or GEMINI_API_KEY environment variable not set")

            # Configure Gemini
            genai.configure(api_key=api_key)

            # Initialize model
            self.gemini_model = genai.GenerativeModel(self.primary_model)
            self.gemini_fallback_model = genai.GenerativeModel(self.fallback_model)

            self.logger.info(f"✅ Direct Gemini client initialized with models: {self.primary_model}, {self.fallback_model}")

        except ImportError:
            self.logger.error("❌ google-generativeai library not installed. Install with: pip install google-generativeai")
            raise
        except Exception as e:
            self.logger.error(f"❌ Failed to initialize Gemini client: {e}")
            raise

    def _get_alternating_model(self):
        """Get alternating model for load balancing"""
        self.request_counter += 1

        # Alternate between primary and fallback models
        if self.request_counter % 2 == 1:
            return self.gemini_model, self.primary_model
        else:
            return self.gemini_fallback_model, self.fallback_model

    async def _call_gemini_direct(self, system_prompt: str, user_prompt: str, temperature: float = 0.1, max_tokens: int = 1500) -> Dict[str, Any]:
        """Call Gemini API directly with alternating models for load balancing"""
        try:
            # Combine system and user prompts for Gemini
            combined_prompt = f"{system_prompt}\n\nUser Request:\n{user_prompt}"

            # Configure generation parameters
            generation_config = {
                'temperature': temperature,
                'max_output_tokens': max_tokens,
                'top_p': 0.8,
                'top_k': 40
            }

            # Get alternating model for load balancing
            current_model, model_name = self._get_alternating_model()

            self.logger.info(f"🔄 Using alternating model: {model_name} (request #{self.request_counter})")

            # Make the API call with alternating model
            response = await current_model.generate_content_async(
                combined_prompt,
                generation_config=generation_config
            )

            if response and response.text:
                # Track the model used
                self.last_model_used = model_name
                return {
                    'content': response.text,
                    'model': model_name,
                    'provider': 'gemini_direct'
                }
            else:
                self.logger.warning(f"❌ Empty response from {model_name}")
                return None

        except Exception as e:
            self.logger.warning(f"❌ Gemini call failed with {model_name}: {e}")

            # Try the other model as fallback
            try:
                other_model = self.gemini_fallback_model if current_model == self.gemini_model else self.gemini_model
                other_model_name = self.fallback_model if current_model == self.gemini_model else self.primary_model

                self.logger.info(f"🔄 Trying fallback model: {other_model_name}")

                response = await other_model.generate_content_async(
                    combined_prompt,
                    generation_config=generation_config
                )

                if response and response.text:
                    self.logger.info(f"✅ Fallback model {other_model_name} succeeded")
                    # Track the fallback model used
                    self.last_model_used = other_model_name
                    return {
                        'content': response.text,
                        'model': other_model_name,
                        'provider': 'gemini_direct'
                    }
            except Exception as fallback_error:
                self.logger.error(f"❌ Fallback model also failed: {fallback_error}")

            return None

    def _load_domain_patterns(self) -> Dict[str, Any]:
        """Load expanded domain classification patterns - 50+ domains"""
        return {
            # TECHNOLOGY DOMAINS
            "mobile_app_development": {
                "keywords": ["app", "mobile", "ios", "android", "ứng dụng", "di động", "flutter", "react native"],
                "indicators": ["tải về", "app store", "play store", "smartphone", "tablet", "cross-platform"],
                "complexity_factors": ["payment", "real-time", "offline", "push notification", "camera", "biometric"],
                "sub_domains": ["ios_development", "android_development", "cross_platform", "mobile_games"]
            },
            "web_development": {
                "keywords": ["website", "web", "trang web", "online", "browser", "frontend", "backend"],
                "indicators": ["domain", "hosting", "responsive", "seo", "url", "spa", "pwa"],
                "complexity_factors": ["e-commerce", "cms", "api", "real-time", "multi-language", "microservices"],
                "sub_domains": ["frontend_development", "backend_development", "fullstack", "jamstack"]
            },
            "data_science": {
                "keywords": ["phân tích", "dữ liệu", "machine learning", "AI", "analytics", "data", "ml", "deep learning"],
                "indicators": ["dataset", "model", "prediction", "algorithm", "visualization", "tensorflow", "pytorch"],
                "complexity_factors": ["big data", "real-time", "deep learning", "nlp", "computer vision", "mlops"],
                "sub_domains": ["machine_learning", "data_analytics", "computer_vision", "nlp", "data_engineering"]
            },
            "e_commerce": {
                "keywords": ["bán hàng", "shop", "cửa hàng", "thương mại", "commerce", "store", "marketplace"],
                "indicators": ["thanh toán", "giỏ hàng", "sản phẩm", "đơn hàng", "inventory", "checkout"],
                "complexity_factors": ["multi-vendor", "international", "subscription", "marketplace", "omnichannel"],
                "sub_domains": ["b2c_ecommerce", "b2b_marketplace", "c2c_platform", "social_commerce"]
            },

            # FINTECH DOMAINS
            "fintech": {
                "keywords": ["fintech", "tài chính", "ngân hàng", "thanh toán", "payment", "banking", "lending"],
                "indicators": ["ví điện tử", "chuyển tiền", "cho vay", "đầu tư", "bảo hiểm", "blockchain"],
                "complexity_factors": ["kyc", "aml", "pci_dss", "regulatory", "security", "real_time_fraud"],
                "sub_domains": ["digital_banking", "payment_systems", "lending_platforms", "investment_tech"]
            },
            "blockchain_development": {
                "keywords": ["blockchain", "crypto", "bitcoin", "ethereum", "smart contract", "defi", "nft", "web3"],
                "indicators": ["wallet", "mining", "token", "consensus", "decentralized", "dao", "dapp"],
                "complexity_factors": ["smart_contracts", "consensus_mechanisms", "security_audits", "gas_optimization"],
                "sub_domains": ["defi_protocols", "nft_platforms", "dao_governance", "crypto_exchanges"]
            },
            "cryptocurrency": {
                "keywords": ["cryptocurrency", "crypto", "trading", "exchange", "wallet", "tiền điện tử"],
                "indicators": ["bitcoin", "ethereum", "altcoin", "trading_bot", "portfolio", "staking"],
                "complexity_factors": ["market_data", "trading_algorithms", "security", "regulatory_compliance"],
                "sub_domains": ["crypto_trading", "wallet_services", "mining_pools", "defi_yield_farming"]
            },

            # HEALTHCARE DOMAINS
            "healthcare_tech": {
                "keywords": ["y tế", "bệnh viện", "healthcare", "medical", "patient", "doctor", "telemedicine"],
                "indicators": ["emr", "ehr", "diagnosis", "prescription", "medical_records", "telehealth"],
                "complexity_factors": ["hipaa_compliance", "medical_standards", "data_privacy", "interoperability"],
                "sub_domains": ["telemedicine", "emr_systems", "medical_devices", "health_analytics"]
            },
            "mental_health": {
                "keywords": ["tâm lý", "mental health", "therapy", "wellness", "meditation", "mindfulness"],
                "indicators": ["counseling", "stress", "anxiety", "depression", "self_care", "mood_tracking"],
                "complexity_factors": ["privacy_sensitive", "professional_licensing", "crisis_intervention"],
                "sub_domains": ["therapy_platforms", "wellness_apps", "meditation_apps", "mental_health_analytics"]
            },

            # EDUCATION DOMAINS
            "edtech": {
                "keywords": ["education", "learning", "course", "training", "lms", "teaching", "student", "classroom", "curriculum", "pedagogy", "academic", "educational", "instructional", "tutorial", "lesson", "study", "school", "university", "college"],
                "indicators": ["online_learning", "e_learning", "mooc", "certification", "assessment", "gamification", "virtual_classroom", "interactive_learning", "adaptive_learning", "personalized_learning", "blended_learning", "microlearning", "peer_learning", "collaborative_learning", "experiential_learning", "immersive_learning", "simulation_based", "competency_based"],
                "complexity_factors": ["content_management", "progress_tracking", "multi_device", "accessibility", "virtual_reality", "augmented_reality", "artificial_intelligence", "machine_learning", "analytics_dashboard", "real_time_feedback", "automated_grading", "plagiarism_detection", "learning_analytics"],
                "sub_domains": ["online_learning", "lms_platforms", "educational_games", "skill_assessment", "virtual_reality_learning", "augmented_reality_learning", "educational_simulations", "tutoring_platforms", "professional_development", "corporate_training", "compliance_training", "certification_platforms"]
            },
            "language_learning": {
                "keywords": ["language", "translation", "pronunciation", "vocabulary", "grammar", "speaking", "listening", "conversation", "fluency", "accent", "dialect", "phonetics", "comprehension", "linguistic", "multilingual", "bilingual"],
                "indicators": ["speaking_practice", "listening_comprehension", "reading_skills", "writing_improvement", "pronunciation_training", "vocabulary_building", "grammar_exercises", "conversation_practice", "language_exchange", "cultural_context", "native_speaker", "language_tutor", "speech_recognition", "accent_training", "oral_communication"],
                "complexity_factors": ["speech_recognition", "ai_tutoring", "adaptive_learning", "cultural_context", "real_time_feedback", "pronunciation_analysis", "fluency_assessment", "interactive_dialogue"],
                "sub_domains": ["language_apps", "translation_services", "pronunciation_training", "cultural_learning", "conversation_practice", "listening_training", "vocabulary_building", "grammar_learning"]
            },

            # EMERGING TECH DOMAINS
            "ai_ml_development": {
                "keywords": ["artificial intelligence", "machine learning", "neural network", "ai", "ml", "automation", "deep_learning", "algorithm", "model", "training", "prediction", "classification", "regression", "clustering", "reinforcement_learning", "ai_powered", "intelligent", "smart_algorithm"],
                "indicators": ["tensorflow", "pytorch", "model_training", "inference", "computer_vision", "nlp", "deep_learning", "predictive_analytics", "recommendation_system", "chatbot", "intelligent_system", "automated_analysis", "pattern_recognition", "data_mining", "feature_extraction", "model_deployment", "ai_enhancement", "content_generator"],
                "complexity_factors": ["model_optimization", "scalability", "real_time_inference", "edge_deployment", "big_data", "gpu_computing", "algorithm_optimization", "neural_architecture", "hyperparameter_tuning", "distributed_computing"],
                "sub_domains": ["computer_vision", "natural_language_processing", "robotics", "ai_automation", "ai_education_tools", "intelligent_tutoring", "automated_content_generation", "smart_assessment"]
            },
            "iot_development": {
                "keywords": ["iot", "internet of things", "sensor", "embedded", "arduino", "raspberry pi", "smart_device", "connected", "smart_home", "smart_building", "smart_classroom", "smart_city", "wearable", "beacon", "rfid"],
                "indicators": ["mqtt", "sensor_data", "automation", "monitoring", "remote_control", "edge_computing", "device_connectivity", "smart_sensors", "environmental_monitoring", "location_tracking", "proximity_detection", "automated_control", "real_time_monitoring"],
                "complexity_factors": ["real_time_processing", "edge_computing", "device_management", "connectivity", "mesh_networking", "low_power_design", "security_protocols", "data_aggregation"],
                "sub_domains": ["smart_home", "industrial_iot", "wearable_devices", "smart_city", "smart_classroom", "environmental_monitoring", "asset_tracking", "smart_buildings"]
            },
            "ar_vr_development": {
                "keywords": ["ar", "vr", "augmented reality", "virtual reality", "metaverse", "3d", "immersive", "mixed_reality", "extended_reality", "xr", "virtual_environment", "augmented_experience", "immersive_learning", "virtual_simulation", "3d_visualization"],
                "indicators": ["unity", "unreal", "oculus", "hololens", "spatial_computing", "haptic", "vr_headset", "ar_glasses", "motion_tracking", "gesture_recognition", "spatial_mapping", "immersive_experience", "virtual_training", "simulation_based", "3d_interaction"],
                "complexity_factors": ["3d_rendering", "real_time_tracking", "hardware_integration", "user_experience", "motion_sickness_prevention", "spatial_audio", "hand_tracking", "eye_tracking"],
                "sub_domains": ["ar_applications", "vr_experiences", "metaverse_platforms", "spatial_computing", "vr_learning", "ar_learning", "virtual_training", "immersive_education"]
            },

            # BUSINESS DOMAINS
            "enterprise_software": {
                "keywords": ["doanh nghiệp", "enterprise", "quản lý", "management", "erp", "crm", "business"],
                "indicators": ["workflow", "automation", "reporting", "integration", "scalability", "compliance"],
                "complexity_factors": ["multi_tenant", "compliance", "security", "integration", "scalability"],
                "sub_domains": ["erp_systems", "crm_platforms", "workflow_automation", "business_intelligence"]
            },
            "saas_development": {
                "keywords": ["saas", "software as a service", "subscription", "cloud", "multi_tenant"],
                "indicators": ["recurring_revenue", "scalability", "api", "integration", "analytics"],
                "complexity_factors": ["multi_tenancy", "scalability", "security", "billing", "compliance"],
                "sub_domains": ["b2b_saas", "b2c_saas", "vertical_saas", "platform_as_a_service"]
            },

            # INDUSTRY VERTICALS
            "mobility_tech": {
                "keywords": ["di chuyển", "transportation", "ride_sharing", "logistics", "delivery", "fleet"],
                "indicators": ["gps", "routing", "tracking", "booking", "driver", "passenger"],
                "complexity_factors": ["real_time_tracking", "route_optimization", "payment_integration", "safety"],
                "sub_domains": ["ride_sharing", "food_delivery", "logistics_platforms", "fleet_management"]
            },
            "proptech": {
                "keywords": ["bất động sản", "real estate", "property", "rental", "housing", "construction"],
                "indicators": ["listing", "valuation", "mortgage", "property_management", "smart_building"],
                "complexity_factors": ["market_data", "legal_compliance", "financial_integration", "iot_integration"],
                "sub_domains": ["property_platforms", "rental_management", "smart_buildings", "construction_tech"]
            },
            "agtech": {
                "keywords": ["nông nghiệp", "agriculture", "farming", "crop", "livestock", "precision_farming"],
                "indicators": ["irrigation", "monitoring", "yield", "weather", "soil", "drone"],
                "complexity_factors": ["iot_sensors", "weather_data", "machine_learning", "automation"],
                "sub_domains": ["precision_farming", "crop_monitoring", "livestock_management", "supply_chain"]
            },
            "cleantech": {
                "keywords": ["năng lượng", "energy", "renewable", "solar", "wind", "sustainability", "green"],
                "indicators": ["carbon", "emission", "efficiency", "grid", "battery", "smart_meter"],
                "complexity_factors": ["grid_integration", "energy_storage", "regulatory", "sustainability_metrics"],
                "sub_domains": ["renewable_energy", "energy_management", "carbon_tracking", "smart_grid"]
            },

            # MEDIA & ENTERTAINMENT
            "media_tech": {
                "keywords": ["media", "content", "streaming", "video", "audio", "entertainment", "social"],
                "indicators": ["cdn", "encoding", "live_streaming", "content_delivery", "recommendation"],
                "complexity_factors": ["content_delivery", "scalability", "recommendation_engines", "content_moderation"],
                "sub_domains": ["streaming_platforms", "content_creation", "social_media", "digital_publishing"]
            },
            "creator_economy": {
                "keywords": ["creator", "influencer", "content_creator", "monetization", "subscription", "fan"],
                "indicators": ["followers", "engagement", "sponsorship", "merchandise", "community"],
                "complexity_factors": ["payment_processing", "community_management", "content_moderation", "analytics"],
                "sub_domains": ["influencer_platforms", "fan_engagement", "creator_tools", "monetization_platforms"]
            },

            # TRADITIONAL ENHANCED
            "game_development": {
                "keywords": ["game", "trò chơi", "gaming", "entertainment", "esports", "mobile_game"],
                "indicators": ["graphics", "animation", "multiplayer", "leaderboard", "achievements", "unity"],
                "complexity_factors": ["3d", "multiplayer", "real-time", "physics", "ai", "monetization"],
                "sub_domains": ["mobile_games", "pc_games", "console_games", "vr_games", "esports_platforms"]
            }
        }

    def _load_requirement_patterns(self) -> Dict[str, Any]:
        """Load requirement extraction patterns"""
        return {
            "functional": {
                "user_management": ["đăng nhập", "tài khoản", "user", "authentication", "register", "login"],
                "payment_processing": ["thanh toán", "payment", "momo", "visa", "mastercard", "paypal", "stripe"],
                "product_catalog": ["sản phẩm", "danh mục", "catalog", "inventory", "product", "category"],
                "search_functionality": ["tìm kiếm", "search", "filter", "sort", "query"],
                "shopping_cart": ["giỏ hàng", "cart", "add to cart", "checkout", "basket"],
                "order_management": ["đơn hàng", "order", "tracking", "history", "status"],
                "reviews_ratings": ["đánh giá", "review", "rating", "comment", "feedback"],
                "notifications": ["thông báo", "notification", "alert", "push", "email"],
                "social_features": ["share", "like", "follow", "comment", "social", "friend"],
                "content_management": ["content", "cms", "blog", "article", "post", "upload"],
                "reporting_analytics": ["báo cáo", "report", "analytics", "dashboard", "statistics"],
                "messaging_chat": ["chat", "message", "conversation", "support", "communication"]
            },
            "non_functional": {
                "performance": ["nhanh", "fast", "performance", "tốc độ", "speed", "optimization"],
                "security": ["bảo mật", "secure", "an toàn", "encryption", "ssl", "authentication"],
                "scalability": ["mở rộng", "scale", "nhiều user", "traffic", "load", "growth"],
                "usability": ["dễ dùng", "user-friendly", "intuitive", "UX", "interface", "experience"],
                "reliability": ["ổn định", "reliable", "uptime", "available", "stable", "robust"],
                "mobile_responsive": ["mobile", "responsive", "điện thoại", "tablet", "adaptive"],
                "accessibility": ["accessibility", "disabled", "screen reader", "wcag"],
                "maintainability": ["maintain", "update", "modify", "extend", "flexible"],
                "compatibility": ["compatible", "cross-platform", "browser", "device", "version"]
            },
            "technical": {
                "mobile_app": ["app", "mobile", "ios", "android", "react native", "flutter"],
                "web_application": ["website", "web app", "browser", "html", "css", "javascript"],
                "backend_api": ["api", "backend", "server", "rest", "graphql", "microservices"],
                "database_design": ["database", "data storage", "mysql", "mongodb", "postgresql"],
                "cloud_hosting": ["cloud", "aws", "azure", "gcp", "hosting", "deployment"],
                "third_party_integration": ["integration", "api", "payment gateway", "social login"],
                "real_time_features": ["real-time", "websocket", "live", "instant", "streaming"],
                "ai_ml_features": ["ai", "machine learning", "recommendation", "prediction", "nlp"]
            }
        }

    def _classify_primary_domain(self, user_input: str) -> Tuple[str, float, Dict[str, Any]]:
        """Classify primary domain based on keywords and patterns"""
        scores = {}
        user_input_lower = user_input.lower()

        for domain, patterns in self.domain_patterns.items():
            score = 0

            # Keyword matching (base score)
            for keyword in patterns["keywords"]:
                if keyword.lower() in user_input_lower:
                    score += 2

            # Indicator matching (higher weight)
            for indicator in patterns["indicators"]:
                if indicator.lower() in user_input_lower:
                    score += 3

            # Complexity factor detection
            complexity_score = 0
            for factor in patterns["complexity_factors"]:
                if factor.lower() in user_input_lower:
                    complexity_score += 1

            scores[domain] = {
                "base_score": score,
                "complexity_score": complexity_score,
                "total_score": score + complexity_score * 0.5
            }

        # Find best match
        if not scores or all(score["total_score"] == 0 for score in scores.values()):
            # Default fallback
            best_domain = "web_development"
            confidence = 0.3
        else:
            best_domain = max(scores.keys(), key=lambda x: scores[x]["total_score"])
            max_score = scores[best_domain]["total_score"]

            # Improved confidence calculation
            if max_score >= 8:
                confidence = 0.95
            elif max_score >= 6:
                confidence = 0.85
            elif max_score >= 4:
                confidence = 0.75
            elif max_score >= 2:
                confidence = 0.65
            else:
                confidence = 0.45

        return best_domain, confidence, scores

    def _extract_requirements(self, user_input: str, primary_domain: str) -> Dict[str, List[str]]:
        """Extract functional, non-functional, and technical requirements"""
        extracted = {
            "functional": [],
            "non_functional": [],
            "technical": []
        }

        user_input_lower = user_input.lower()

        for req_type, categories in self.requirement_patterns.items():
            for req_name, keywords in categories.items():
                for keyword in keywords:
                    if keyword.lower() in user_input_lower:
                        if req_name not in extracted[req_type]:
                            extracted[req_type].append(req_name)
                        break

        # Domain-specific requirement enhancement
        self._enhance_requirements_by_domain(extracted, primary_domain, user_input_lower)

        return extracted

    def _enhance_requirements_by_domain(self, requirements: Dict[str, List[str]],
                                      domain: str, user_input: str) -> None:
        """Add domain-specific requirements based on context"""

        if domain == "mobile_app_development":
            if any(keyword in user_input for keyword in ["e-commerce", "bán hàng", "shop"]):
                requirements["functional"].extend([
                    "product_catalog", "shopping_cart", "payment_processing", "user_management"
                ])
                requirements["technical"].extend([
                    "mobile_app", "backend_api", "database_design"
                ])

            # Always add mobile-specific requirements
            if "mobile_app" not in requirements["technical"]:
                requirements["technical"].append("mobile_app")
            if "mobile_responsive" not in requirements["non_functional"]:
                requirements["non_functional"].append("mobile_responsive")

        elif domain == "e_commerce":
            # E-commerce essentials
            essential_functional = ["product_catalog", "shopping_cart", "payment_processing",
                                  "user_management", "order_management"]
            for req in essential_functional:
                if req not in requirements["functional"]:
                    requirements["functional"].append(req)

            essential_technical = ["backend_api", "database_design", "third_party_integration"]
            for req in essential_technical:
                if req not in requirements["technical"]:
                    requirements["technical"].append(req)

        elif domain == "web_development":
            if "web_application" not in requirements["technical"]:
                requirements["technical"].append("web_application")
            if "mobile_responsive" not in requirements["non_functional"]:
                requirements["non_functional"].append("mobile_responsive")

        # Remove duplicates while preserving order
        for req_type in requirements:
            requirements[req_type] = list(dict.fromkeys(requirements[req_type]))

    def _analyze_constraints(self, user_input: str, duration: str) -> Dict[str, Any]:
        """Analyze project constraints from input"""

        constraints = {
            "time": self._parse_duration(duration),
            "budget": "medium",  # Default
            "team_size": "small_team_3_5_people",  # Default
            "technical_expertise": "intermediate"  # Default
        }

        user_input_lower = user_input.lower()

        # Budget indicators
        budget_indicators = {
            "low": ["ít tiền", "budget thấp", "tiết kiệm", "rẻ", "free", "miễn phí"],
            "high": ["không giới hạn", "cao cấp", "premium", "enterprise", "unlimited"]
        }

        for budget_level, indicators in budget_indicators.items():
            for indicator in indicators:
                if indicator in user_input_lower:
                    constraints["budget"] = budget_level
                    break

        # Team size indicators
        team_indicators = {
            "solo": ["một mình", "tự làm", "solo", "individual", "freelancer"],
            "large_team_10_plus": ["team lớn", "nhiều người", "công ty", "enterprise", "organization", "large team"]
        }

        for team_size, indicators in team_indicators.items():
            for indicator in indicators:
                if indicator in user_input_lower:
                    constraints["team_size"] = team_size
                    break

        # Technical expertise indicators
        expertise_indicators = {
            "beginner": ["mới bắt đầu", "beginner", "học", "không biết", "newbie"],
            "advanced": ["chuyên gia", "expert", "advanced", "professional", "senior"]
        }

        for level, indicators in expertise_indicators.items():
            for indicator in indicators:
                if indicator in user_input_lower:
                    constraints["technical_expertise"] = level
                    break

        return constraints

    def _parse_duration(self, duration_str: str) -> str:
        """Parse duration string into standardized format"""
        duration_lower = duration_str.lower().strip()

        # Extract number and unit

        # Handle cases like "3 months", "2 weeks", etc.
        if "tuần" in duration_lower or "week" in duration_lower:
            # Extract number if present
            numbers = re.findall(r'\d+', duration_str)
            if numbers:
                return f"{numbers[0]}_weeks"
            else:
                return "1_weeks"
        elif "tháng" in duration_lower or "month" in duration_lower:
            numbers = re.findall(r'\d+', duration_str)
            if numbers:
                return f"{numbers[0]}_months"
            else:
                return "3_months"
        elif "năm" in duration_lower or "year" in duration_lower:
            numbers = re.findall(r'\d+', duration_str)
            if numbers:
                return f"{numbers[0]}_years"
            else:
                return "1_years"
        else:
            # Try to extract just numbers and assume months
            numbers = re.findall(r'\d+', duration_str)
            if numbers:
                return f"{numbers[0]}_months"
            else:
                return duration_str

    def _identify_success_metrics(self, domain: str, requirements: Dict[str, List[str]]) -> List[str]:
        """Identify relevant success metrics based on domain and requirements"""

        domain_metrics = {
            "mobile_app_development": [
                "app_store_rating",
                "download_count",
                "user_retention_rate",
                "daily_active_users",
                "crash_rate"
            ],
            "e_commerce": [
                "conversion_rate",
                "average_order_value",
                "customer_acquisition_cost",
                "customer_lifetime_value",
                "cart_abandonment_rate"
            ],
            "web_development": [
                "page_load_speed",
                "bounce_rate",
                "user_engagement_time",
                "seo_ranking",
                "conversion_rate"
            ],
            "data_science": [
                "model_accuracy",
                "data_quality_score",
                "processing_time",
                "prediction_accuracy",
                "user_adoption_rate"
            ]
        }

        base_metrics = domain_metrics.get(domain, [
            "user_adoption_rate",
            "user_satisfaction_score",
            "system_uptime",
            "performance_metrics"
        ])

        # Add requirement-specific metrics
        functional_reqs = requirements.get("functional", [])
        if "payment_processing" in functional_reqs:
            base_metrics.append("payment_success_rate")

        if "user_management" in functional_reqs:
            base_metrics.append("user_registration_rate")

        if "search_functionality" in functional_reqs:
            base_metrics.append("search_success_rate")

        return base_metrics[:5]  # Limit to top 5 metrics

    def _identify_stakeholders(self, domain: str, requirements: Dict[str, List[str]]) -> List[str]:
        """Identify project stakeholders"""

        base_stakeholders = ["end_users", "development_team", "project_owner"]

        # Domain-specific stakeholders
        if domain == "e_commerce":
            base_stakeholders.extend(["customers", "vendors", "payment_providers"])
        elif domain == "mobile_app_development":
            base_stakeholders.extend(["app_store_reviewers", "device_manufacturers"])
        elif domain == "enterprise_software":
            base_stakeholders.extend(["system_administrators", "compliance_officers"])
        elif domain == "data_science":
            base_stakeholders.extend(["data_scientists", "business_analysts", "data_owners"])

        # Requirement-specific stakeholders
        functional_reqs = requirements.get("functional", [])
        if "payment_processing" in functional_reqs:
            base_stakeholders.append("financial_institutions")

        if "user_management" in functional_reqs:
            base_stakeholders.append("data_protection_officers")

        if "content_management" in functional_reqs:
            base_stakeholders.append("content_creators")

        return list(set(base_stakeholders))  # Remove duplicates

    def _extract_sub_domains(self, user_input: str, primary_domain: str) -> List[str]:
        """Extract relevant sub-domains based on input and primary domain"""
        sub_domains = []
        user_input_lower = user_input.lower()

        # Common sub-domain patterns
        sub_domain_patterns = {
            "e_commerce": ["bán hàng", "shop", "store", "commerce", "marketplace"],
            "fashion_retail": ["quần áo", "thời trang", "fashion", "clothing", "apparel"],
            "social_commerce": ["social", "share", "community", "friend", "follow"],
            "user_experience": ["ux", "ui", "experience", "interface", "design"],
            "payment_systems": ["thanh toán", "payment", "billing", "transaction"],
            "inventory_management": ["kho", "inventory", "stock", "warehouse"],
            "customer_service": ["hỗ trợ", "support", "service", "help", "chat"],
            "analytics": ["phân tích", "analytics", "report", "dashboard", "metrics"],
            "content_management": ["content", "cms", "blog", "article", "media"],
            "real_time_features": ["real-time", "live", "instant", "streaming"],
            "mobile_optimization": ["mobile", "responsive", "app", "smartphone"],
            "security": ["bảo mật", "security", "authentication", "encryption"]
        }

        # Extract sub-domains based on keywords
        for sub_domain, keywords in sub_domain_patterns.items():
            for keyword in keywords:
                if keyword in user_input_lower:
                    if sub_domain not in sub_domains:
                        sub_domains.append(sub_domain)
                    break

        # Add domain-specific sub-domains
        if primary_domain == "mobile_app_development":
            if not any(sub in sub_domains for sub in ["mobile_optimization"]):
                sub_domains.append("mobile_optimization")

        # Ensure we have at least 2-3 sub-domains
        if len(sub_domains) < 2:
            if primary_domain == "mobile_app_development":
                sub_domains.extend(["user_experience", "mobile_optimization"])
            elif primary_domain == "web_development":
                sub_domains.extend(["user_experience", "content_management"])
            elif primary_domain == "e_commerce":
                sub_domains.extend(["payment_systems", "user_experience"])

        return sub_domains[:4]  # Limit to top 4 sub-domains

    def _assess_complexity(self, requirements: Dict[str, List[str]], constraints: Dict[str, Any]) -> str:
        """Assess project complexity level"""
        complexity_score = 0

        # Count requirements
        total_requirements = sum(len(reqs) for reqs in requirements.values())
        if total_requirements > 15:
            complexity_score += 2
        elif total_requirements > 10:
            complexity_score += 1

        # Check for complex features
        complex_features = [
            "payment_processing", "real_time_features", "ai_ml_features",
            "third_party_integration", "scalability", "security"
        ]

        for req_type in requirements.values():
            for req in req_type:
                if req in complex_features:
                    complexity_score += 1

        # Time constraints impact
        time_constraint = constraints.get("time", "")
        if "week" in time_constraint or "tuần" in time_constraint:
            complexity_score += 1

        # Team size impact
        team_size = constraints.get("team_size", "")
        if team_size == "solo":
            complexity_score += 1
        elif team_size == "large_team":
            complexity_score -= 1

        # Determine complexity level
        if complexity_score <= 2:
            return "beginner"
        elif complexity_score <= 5:
            return "intermediate"
        elif complexity_score <= 8:
            return "advanced"
        else:
            return "expert"

    async def _enhance_with_ai(self, preliminary_analysis: Dict[str, Any], user_input: str) -> Dict[str, Any]:
        """Enhanced AI analysis using advanced Gemini techniques"""
        try:
            # Step 1: Determine if we should use advanced Gemini enhancement
            confidence = preliminary_analysis.get("confidence", 0.0)
            sub_domains_count = len(preliminary_analysis.get("sub_domains", []))

            # Use advanced enhancement for low confidence or complex multi-domain projects
            if confidence < 0.7 or sub_domains_count > 2:
                return await self._advanced_gemini_enhance(preliminary_analysis, user_input)
            else:
                # Use basic enhancement for high confidence, simple projects
                return await self._basic_ai_enhance(preliminary_analysis, user_input)

        except Exception as e:
            self.logger.error(f"AI enhancement failed: {e}")
            return await self._basic_ai_enhance(preliminary_analysis, user_input)

    async def _advanced_gemini_enhance(self, preliminary_analysis: Dict[str, Any], user_input: str) -> Dict[str, Any]:
        """Advanced multi-approach Gemini enhancement"""
        try:
            # Step 1: Dynamic prompt selection based on detected domain
            primary_domain = preliminary_analysis.get("primary_domain", "unknown")
            specialized_prompt = self._build_specialized_prompt(primary_domain, user_input)

            # Step 2: Multi-approach Gemini analysis
            results = []

            # Approach 1: Direct classification with specialized prompt
            self.logger.info("Running direct Gemini classification...")
            direct_result = await self._gemini_direct_classify(user_input, specialized_prompt)
            if direct_result:
                results.append(direct_result)
                self.logger.info("Direct classification completed")

            # Approach 2: Hierarchical classification
            self.logger.info("Running hierarchical Gemini classification...")
            hierarchical_result = await self._gemini_hierarchical_classify(user_input)
            if hierarchical_result:
                results.append(hierarchical_result)
                self.logger.info("Hierarchical classification completed")

            # Approach 3: Cross-domain analysis (if multiple domains detected)
            sub_domains_count = len(preliminary_analysis.get("sub_domains", []))
            if sub_domains_count > 2:
                self.logger.info("Running cross-domain analysis...")
                cross_domain_result = await self._gemini_cross_domain_analyze(user_input, preliminary_analysis)
                if cross_domain_result:
                    results.append(cross_domain_result)
                    self.logger.info("Cross-domain analysis completed")

            # Step 3: Ensemble and confidence assessment
            if results:
                self.logger.info(f"Ensembling {len(results)} Gemini results...")
                enhanced_analysis = await self._ensemble_gemini_results(results, preliminary_analysis)

                # Self-assess confidence
                enhanced_analysis["ai_enhancement_confidence"] = await self._gemini_self_assess_confidence(
                    enhanced_analysis, user_input
                )
                enhanced_analysis["enhancement_method"] = "gemini_advanced_multi_approach"
                enhanced_analysis["approaches_used"] = len(results)

                self.logger.info(f"Advanced Gemini enhancement completed with confidence: {enhanced_analysis['ai_enhancement_confidence']:.2f}")
                return enhanced_analysis
            else:
                # No results from advanced approaches, fallback to basic
                self.logger.warning("No results from advanced approaches, falling back to basic enhancement")
                return await self._basic_ai_enhance(preliminary_analysis, user_input)

        except Exception as e:
            self.logger.error(f"Advanced Gemini enhancement failed: {e}")
            # Fallback to basic enhancement
            return await self._basic_ai_enhance(preliminary_analysis, user_input)

    async def _basic_ai_enhance(self, preliminary_analysis: Dict[str, Any], user_input: str) -> Dict[str, Any]:
        """Basic AI enhancement using direct Gemini API"""
        try:
            # Import direct Gemini provider
            from ..prompts.domain_analysis_prompts import (
                DOMAIN_ANALYSIS_SYSTEM_PROMPT,
                format_domain_analysis_prompt
            )

            user_prompt = format_domain_analysis_prompt(
                user_input,
                preliminary_analysis.get("constraints", {}).get("time", "3_months"),
                preliminary_analysis
            )

            ai_response = await self._call_gemini_direct(
                system_prompt=DOMAIN_ANALYSIS_SYSTEM_PROMPT,
                user_prompt=user_prompt,
                temperature=self.temperature,
                max_tokens=self.max_tokens
            )

            # Parse AI response
            ai_content = ai_response.get("content", "").strip()
            if ai_content.startswith("```json"):
                ai_content = ai_content[7:-3].strip()
            elif ai_content.startswith("```"):
                ai_content = ai_content[3:-3].strip()

            enhanced_analysis = json.loads(ai_content)

            # Merge with preliminary analysis, prioritizing AI enhancements
            final_analysis = preliminary_analysis.copy()
            final_analysis.update(enhanced_analysis)

            # Add AI confidence score
            final_analysis["ai_enhancement_confidence"] = 0.8
            final_analysis["enhancement_method"] = "gemini_basic"

            self.logger.info("Basic AI enhancement completed successfully")
            return final_analysis

        except ImportError:
            self.logger.warning("AI provider not available, using preliminary analysis")
            preliminary_analysis["ai_enhancement_confidence"] = 0.0
            preliminary_analysis["enhancement_method"] = "pattern_based_only"
            return preliminary_analysis

        except json.JSONDecodeError as e:
            self.logger.warning(f"AI response parsing failed: {e}")
            preliminary_analysis["ai_enhancement_confidence"] = 0.5
            preliminary_analysis["enhancement_method"] = "pattern_based_with_ai_error"
            return preliminary_analysis

        except Exception as e:
            self.logger.warning(f"Basic AI enhancement failed: {e}")
            preliminary_analysis["ai_enhancement_confidence"] = 0.0
            preliminary_analysis["enhancement_method"] = "pattern_based_only"
            return preliminary_analysis

    def _build_specialized_prompt(self, domain: str, user_input: str) -> str:
        """Build specialized prompt based on detected domain"""

        specialized_prompts = {
            'fintech': """
            You are a Fintech Domain Expert with 15+ years of experience in:
            - Digital banking, payment systems, lending platforms
            - Regulatory compliance (PCI DSS, KYC, AML)
            - Blockchain and cryptocurrency applications
            - Investment technology and trading platforms

            Analyze the input and accurately identify fintech sub-domain, compliance requirements,
            and appropriate technology stack. Pay special attention to security and regulatory aspects.

            Return JSON format with detailed structure including:
            - primary_domain and sub_domains
            - regulatory_requirements
            - security_considerations
            - technology_recommendations
            """,

            'healthcare_tech': """
            You are a Healthcare Technology Expert specializing in:
            - Electronic Medical Records (EMR/EHR)
            - Telemedicine and remote patient monitoring
            - Medical device integration
            - HIPAA compliance and healthcare data security

            Identify healthcare technology domain, privacy requirements, and medical standards compliance.
            Pay attention to patient data protection and interoperability.

            Return JSON with healthcare-specific analysis.
            """,

            'blockchain_development': """
            You are a Blockchain & Web3 Expert with expertise in:
            - Smart contract development (Solidity, Rust)
            - DeFi protocols and tokenomics
            - NFT platforms and marketplace
            - DAO governance and decentralized systems

            Analyze blockchain use case, identify appropriate consensus mechanism,
            and assess security considerations for smart contracts.

            Return JSON with blockchain-specific technical requirements.
            """,

            'ai_ml_development': """
            You are an AI/ML Engineering Expert specializing in:
            - Machine learning model development and deployment
            - Computer vision and natural language processing
            - MLOps and model lifecycle management
            - Edge AI and real-time inference

            Identify appropriate AI/ML approach, data requirements, and infrastructure needs.
            Assess model complexity and deployment challenges.

            Return JSON with AI/ML-specific analysis.
            """,

            'mobility_tech': """
            You are a Mobility & Transportation Expert specializing in:
            - Ride-sharing and on-demand transportation
            - Logistics and delivery platforms
            - Fleet management systems
            - Smart transportation solutions

            Analyze mobility use case, identify business model and technical requirements.
            Pay attention to real-time tracking, route optimization, and safety considerations.
            """,

            'iot_development': """
            You are an IoT & Smart Device Expert with expertise in:
            - Sensor networks and edge computing
            - Industrial IoT and automation
            - Smart home and consumer IoT
            - Device management and connectivity

            Identify IoT architecture, connectivity requirements, and edge processing needs.
            Assess scalability and security for IoT deployment.
            """
        }

        base_prompt = """
        You are a Senior Domain Expert with 15+ years of experience analyzing technology projects.

        Please analyze the following input and provide comprehensive domain analysis including:
        1. Primary domain classification with high confidence score
        2. Sub-domains and detailed importance levels
        3. Specific and actionable technical requirements
        4. Complexity assessment with clear reasoning
        5. Success metrics appropriate for the domain
        6. Risk factors and mitigation strategies
        7. Technology stack recommendations with alternatives
        8. Market opportunities and competitive landscape

        Return JSON format with clear structure, complete information, and actionable insights.
        Ensure deep and practical analysis for implementation.
        """

        return specialized_prompts.get(domain, base_prompt)

    async def _gemini_direct_classify(self, user_input: str, specialized_prompt: str) -> Dict[str, Any]:
        """Direct classification using specialized Gemini prompt"""
        try:
            ai_response = await self._call_gemini_direct(
                system_prompt=specialized_prompt,
                user_prompt=f"Analyze in detail input: '{user_input}'",
                temperature=0.1,
                max_tokens=2000
            )

            if ai_response and 'content' in ai_response:
                return self._parse_gemini_response(ai_response['content'], "direct_classify")

        except Exception as e:
            self.logger.warning(f"Direct Gemini classification failed: {e}")

        return None

    async def _gemini_hierarchical_classify(self, user_input: str) -> Dict[str, Any]:
        """Hierarchical classification using multiple Gemini calls"""
        try:
            # Step 1: Major category classification
            major_category_prompt = """
            Identify major category for this input in 3 main categories:

            1. TECHNOLOGY - Software development, AI/ML, Hardware, Infrastructure
               Examples: mobile apps, web platforms, AI systems, IoT devices

            2. BUSINESS - Business models, Services, Platforms, Marketplaces
               Examples: e-commerce, fintech, SaaS, consulting platforms

            3. INDUSTRY - Vertical-specific solutions, Domain expertise required
               Examples: healthcare systems, education platforms, agriculture tech

            Return JSON: {"major_category": "TECHNOLOGY|BUSINESS|INDUSTRY", "confidence": 0.0-1.0, "reasoning": "detailed explanation"}
            """

            major_response = await self._call_gemini_direct(
                system_prompt=major_category_prompt,
                user_prompt=f"Classify: '{user_input}'",
                temperature=0.1,
                max_tokens=500
            )

            if not major_response or 'content' not in major_response:
                return None

            major_result = self._parse_gemini_response(major_response['content'], "major_category")
            if not major_result:
                return None

            # Step 2: Specific domain based on major category
            major_category = major_result.get('major_category', 'TECHNOLOGY')

            domain_prompt = f"""
            Input belongs to {major_category} category. Identify specific domain from the following list:

            {self._get_domains_for_category(major_category)}

            Analyze deeply and return JSON with:
            - primary_domain: most accurate domain
            - confidence: confidence level (0.0-1.0)
            - sub_domains: 2-4 related sub-domains
            - complexity_level: beginner/intermediate/advanced/expert
            - key_requirements: top 5 most important requirements
            - success_metrics: 3-5 appropriate metrics
            - reasoning: detailed explanation for classification
            """

            domain_response = await self._call_gemini_direct(
                system_prompt=domain_prompt,
                user_prompt=f"Analyze: '{user_input}'",
                temperature=0.1,
                max_tokens=1500
            )

            if domain_response and 'content' in domain_response:
                domain_result = self._parse_gemini_response(domain_response['content'], "hierarchical")
                if domain_result:
                    domain_result['major_category'] = major_category
                    domain_result['classification_method'] = 'hierarchical'
                    return domain_result

        except Exception as e:
            self.logger.warning(f"Hierarchical Gemini classification failed: {e}")

        return None

    def _get_domains_for_category(self, category: str) -> str:
        """Get domain list for specific category"""

        domain_lists = {
            'TECHNOLOGY': """
            - mobile_app_development: iOS, Android, Cross-platform apps
            - web_development: Frontend, Backend, Full-stack web applications
            - ai_ml_development: Machine Learning, Computer Vision, NLP, Deep Learning
            - blockchain_development: Smart contracts, DeFi, NFT, Web3 platforms
            - iot_development: Smart devices, Sensor networks, Edge computing
            - ar_vr_development: Augmented Reality, Virtual Reality, Metaverse
            - game_development: Mobile games, PC games, Console games
            - data_science: Analytics, Business Intelligence, Data Engineering
            """,

            'BUSINESS': """
            - e_commerce: Online stores, Marketplaces, B2B/B2C platforms
            - fintech: Digital banking, Payment systems, Investment platforms
            - saas_development: Software as a Service, B2B tools, Cloud platforms
            - marketplace_platforms: Multi-vendor, Service marketplaces
            - subscription_services: Recurring revenue models, Content platforms
            - social_platforms: Social networks, Community platforms, Creator tools
            """,

            'INDUSTRY': """
            - healthcare_tech: EMR/EHR, Telemedicine, Medical devices
            - edtech: Learning platforms, Online courses, Educational tools
            - proptech: Real estate platforms, Property management, Smart buildings
            - agtech: Agriculture technology, Precision farming, Supply chain
            - cleantech: Renewable energy, Sustainability, Carbon tracking
            - mobility_tech: Transportation, Ride-sharing, Logistics platforms
            - media_tech: Content platforms, Streaming, Digital publishing
            """
        }

        return domain_lists.get(category, "Various technology and business domains")

    async def _gemini_cross_domain_analyze(self, user_input: str, preliminary_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze projects spanning multiple domains"""
        try:
            detected_domains = preliminary_analysis.get("sub_domains", [])

            cross_domain_prompt = f"""
            Input: "{user_input}"

            This project appears to span across multiple domains: {detected_domains}

            Please analyze this cross-domain project:

            1. PRIMARY DOMAIN (main domain, >50% effort and complexity)
            2. SECONDARY DOMAINS (supporting domains, 20-40% effort each)
            3. INTEGRATION COMPLEXITY (complexity when integrating domains)
            4. RECOMMENDED APPROACH (strategy to approach multi-domain project)
            5. POTENTIAL CHALLENGES (challenges when combining domains)
            6. SYNERGY OPPORTUNITIES (opportunities to create synergy between domains)
            7. RESOURCE ALLOCATION (resource allocation for each domain)
            8. RISK MITIGATION (risks and mitigation for cross-domain)

            Example analysis:
            - "AI-powered e-commerce with blockchain payment"
              Primary: e_commerce (60%), Secondary: ai_ml (25%), blockchain (15%)
              Integration: High complexity, requires expertise in all domains

            Return JSON with detailed cross-domain analysis.
            """

            response = await self._call_gemini_direct(
                system_prompt=cross_domain_prompt,
                user_prompt=f"Analyze cross-domain project: '{user_input}'",
                temperature=0.2,
                max_tokens=2000
            )

            if response and 'content' in response:
                result = self._parse_gemini_response(response['content'], "cross_domain")
                if result:
                    result['classification_method'] = 'cross_domain'
                    return result

        except Exception as e:
            self.logger.warning(f"Cross-domain Gemini analysis failed: {e}")

        return None

    async def _ensemble_gemini_results(self, results: List[Dict[str, Any]], preliminary_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Ensemble multiple Gemini results using voting and confidence weighting"""
        try:
            if not results:
                return preliminary_analysis

            # Collect domain votes with confidence weighting
            domain_votes = {}
            total_weight = 0

            for result in results:
                domain = result.get('primary_domain', 'unknown')
                confidence = result.get('confidence', 0.5)

                if domain not in domain_votes:
                    domain_votes[domain] = {'votes': 0, 'total_confidence': 0, 'results': []}

                domain_votes[domain]['votes'] += 1
                domain_votes[domain]['total_confidence'] += confidence
                domain_votes[domain]['results'].append(result)
                total_weight += confidence

            # Find winning domain
            best_domain = None
            best_score = 0

            for domain, data in domain_votes.items():
                # Score = (votes * avg_confidence) / total_results
                avg_confidence = data['total_confidence'] / data['votes']
                score = (data['votes'] * avg_confidence) / len(results)

                if score > best_score:
                    best_score = score
                    best_domain = domain

            if not best_domain:
                return preliminary_analysis

            # Build ensemble result
            winning_results = domain_votes[best_domain]['results']
            ensemble_result = preliminary_analysis.copy()

            # Merge information from winning results
            ensemble_result['primary_domain'] = best_domain
            ensemble_result['confidence'] = domain_votes[best_domain]['total_confidence'] / domain_votes[best_domain]['votes']

            # Aggregate sub_domains
            all_sub_domains = set()
            for result in winning_results:
                sub_domains = result.get('sub_domains', [])
                if isinstance(sub_domains, list):
                    all_sub_domains.update(sub_domains)
            ensemble_result['sub_domains'] = list(all_sub_domains)

            # Aggregate requirements
            all_requirements = {'functional': set(), 'non_functional': set(), 'technical': set()}
            for result in winning_results:
                reqs = result.get('requirements', {})
                for req_type, req_list in reqs.items():
                    if req_type in all_requirements and isinstance(req_list, list):
                        all_requirements[req_type].update(req_list)

            ensemble_result['requirements'] = {k: list(v) for k, v in all_requirements.items()}

            # Take best complexity assessment
            complexity_scores = {'beginner': 1, 'intermediate': 2, 'advanced': 3, 'expert': 4}
            max_complexity = 'beginner'
            for result in winning_results:
                complexity = result.get('complexity_level', 'beginner')
                if complexity_scores.get(complexity, 1) > complexity_scores.get(max_complexity, 1):
                    max_complexity = complexity
            ensemble_result['complexity_level'] = max_complexity

            # Ensemble metadata
            ensemble_result['ensemble_info'] = {
                'total_approaches': len(results),
                'winning_votes': domain_votes[best_domain]['votes'],
                'ensemble_confidence': best_score,
                'methods_used': [r.get('classification_method', 'unknown') for r in results]
            }

            self.logger.info(f"Ensemble completed: {best_domain} with {domain_votes[best_domain]['votes']}/{len(results)} votes")
            return ensemble_result

        except Exception as e:
            self.logger.warning(f"Ensemble failed: {e}")
            return preliminary_analysis

    async def _gemini_self_assess_confidence(self, classification: Dict[str, Any], user_input: str) -> float:
        """Ask Gemini to assess its own classification confidence"""
        try:
            confidence_prompt = f"""
            Bạn vừa classify input: "{user_input}"
            Kết quả classification: {classification.get('primary_domain', 'unknown')}

            Hãy đánh giá confidence level (0.0-1.0) cho classification này dựa trên:

            1. CLARITY của user input (0.2 weight)
               - Input có rõ ràng và specific không?
               - Có ambiguity hoặc multiple interpretations không?

            2. DOMAIN CERTAINTY (0.3 weight)
               - Domain identification có chắc chắn không?
               - Có overlap với domains khác không?

            3. SUPPORTING EVIDENCE (0.3 weight)
               - Có đủ keywords và indicators supporting classification?
               - Technical requirements có align với domain không?

            4. ALTERNATIVE POSSIBILITIES (0.2 weight)
               - Có domains khác có thể fit không?
               - Mức độ certainty so với alternatives?

            Trả về JSON:
            {{
                "confidence_score": 0.0-1.0,
                "reasoning": "detailed explanation",
                "clarity_score": 0.0-1.0,
                "certainty_score": 0.0-1.0,
                "evidence_score": 0.0-1.0,
                "alternatives_score": 0.0-1.0,
                "overall_assessment": "high/medium/low confidence explanation"
            }}
            """

            response = await self._call_gemini_direct(
                system_prompt=confidence_prompt,
                user_prompt="Assess confidence for this classification",
                temperature=0.1,
                max_tokens=800
            )

            if response and 'content' in response:
                confidence_result = self._parse_gemini_response(response['content'], "confidence")
                if confidence_result and 'confidence_score' in confidence_result:
                    return float(confidence_result['confidence_score'])

        except Exception as e:
            self.logger.warning(f"Confidence self-assessment failed: {e}")

        return 0.7  # Default confidence

    def _parse_gemini_response(self, content: str, response_type: str) -> Dict[str, Any]:
        """Parse Gemini response content to JSON"""
        try:
            import json

            # Clean content
            content = content.strip()

            # Remove markdown formatting
            if content.startswith('```json'):
                content = content[7:-3].strip()
            elif content.startswith('```'):
                content = content[3:-3].strip()

            # Parse JSON
            result = json.loads(content)
            result['response_type'] = response_type

            return result

        except json.JSONDecodeError as e:
            self.logger.warning(f"Failed to parse {response_type} Gemini response: {e}")
            return None
        except Exception as e:
            self.logger.warning(f"Error processing {response_type} response: {e}")
            return None

    async def process(self, state: PlanGenerationState) -> Dict[str, Any]:
        """
        Main domain analysis process

        Args:
            state: Current plan generation state

        Returns:
            Dict containing domain analysis results
        """
        user_input = state["user_input"]
        duration = state["duration"]

        try:
            self.logger.info("Starting domain classification process...")

            # Step 1: Pattern-based classification
            primary_domain, confidence, domain_scores = self._classify_primary_domain(user_input)
            self.logger.info(f"Primary domain classified as: {primary_domain} (confidence: {confidence:.2f})")

            # Step 2: Extract requirements
            requirements = self._extract_requirements(user_input, primary_domain)
            self.logger.info(f"Extracted {sum(len(reqs) for reqs in requirements.values())} requirements")

            # Step 3: Analyze constraints
            constraints = self._analyze_constraints(user_input, duration)

            # Step 4: Extract sub-domains
            sub_domains = self._extract_sub_domains(user_input, primary_domain)

            # Step 5: Assess complexity
            complexity_level = self._assess_complexity(requirements, constraints)

            # Step 6: Identify success metrics and stakeholders
            success_metrics = self._identify_success_metrics(primary_domain, requirements)
            stakeholders = self._identify_stakeholders(primary_domain, requirements)

            # Step 7: Create preliminary analysis
            preliminary_analysis = {
                "primary_domain": primary_domain,
                "sub_domains": sub_domains,
                "complexity_level": complexity_level,
                "confidence_score": confidence,
                "extracted_requirements": requirements,
                "constraints": constraints,
                "success_metrics": success_metrics,
                "stakeholders": stakeholders,
                "domain_scores": domain_scores,
                "analysis_timestamp": state.get("created_at"),
                "processing_method": "pattern_based"
            }

            # Step 8: Gemini Detection (if pattern-based result is not good)
            final_analysis = await self._apply_gemini_detection_if_needed(preliminary_analysis, user_input)

            # Step 9: AI enhancement (if confidence is still low)
            if final_analysis["confidence_score"] < self.confidence_threshold:
                self.logger.info("Low confidence detected, applying AI enhancement...")
                enhanced_analysis = await self._enhance_with_ai(final_analysis, user_input)
            else:
                self.logger.info("Good confidence, using current analysis")
                enhanced_analysis = final_analysis
                if "ai_enhancement_confidence" not in enhanced_analysis:
                    enhanced_analysis["ai_enhancement_confidence"] = 1.0
                if "enhancement_method" not in enhanced_analysis:
                    enhanced_analysis["enhancement_method"] = "pattern_based_sufficient"

            # Step 10: Add model tracking info
            if self.last_model_used:
                enhanced_analysis["model_used"] = self.last_model_used
            else:
                enhanced_analysis["model_used"] = "pattern_based_only"

            # Step 11: Final validation
            if not self._validate_output({"domain_analysis": enhanced_analysis}):
                raise ValueError("Domain analysis validation failed")

            self.logger.info("Domain classification completed successfully")

            return {
                "domain_analysis": enhanced_analysis
            }

        except Exception as e:
            self.logger.error(f"Domain analysis failed: {e}")
            raise e

    async def _apply_gemini_detection_if_needed(self, preliminary_analysis: Dict[str, Any], user_input: str) -> Dict[str, Any]:
        """
        Apply Gemini detection if pattern-based result is not good enough

        Criteria for using Gemini detection:
        1. Low confidence (< 0.6)
        2. Default fallback domain (web_development with confidence 0.3)
        3. Very low total score (< 3)
        4. Ambiguous cases (multiple domains with similar scores)
        """
        try:
            confidence = preliminary_analysis.get("confidence_score", 0.0)
            primary_domain = preliminary_analysis.get("primary_domain", "")
            domain_scores = preliminary_analysis.get("domain_scores", {})

            # Check if we should use Gemini detection
            should_use_gemini = False
            reason = ""

            # Criterion 1: Low confidence
            if confidence < 0.6:
                should_use_gemini = True
                reason = f"low_confidence_{confidence:.2f}"

            # Criterion 2: Default fallback domain
            elif primary_domain == "web_development" and confidence <= 0.3:
                should_use_gemini = True
                reason = "default_fallback"

            # Criterion 3: Very low total score
            elif domain_scores and primary_domain in domain_scores:
                max_score = domain_scores[primary_domain].get("total_score", 0)
                if max_score < 3:
                    should_use_gemini = True
                    reason = f"low_score_{max_score}"

            # Criterion 4: Ambiguous cases (multiple domains with similar scores)
            elif domain_scores:
                sorted_scores = sorted(domain_scores.items(), key=lambda x: x[1]["total_score"], reverse=True)
                if len(sorted_scores) >= 2:
                    top_score = sorted_scores[0][1]["total_score"]
                    second_score = sorted_scores[1][1]["total_score"]
                    if top_score > 0 and second_score > 0 and (top_score - second_score) <= 1:
                        should_use_gemini = True
                        reason = f"ambiguous_scores_{top_score}_vs_{second_score}"

            if not should_use_gemini:
                self.logger.info(f"Pattern-based result is good enough (confidence: {confidence:.2f}), skipping Gemini detection")
                return preliminary_analysis

            self.logger.info(f"Pattern-based result needs improvement ({reason}), applying Gemini detection...")

            # Apply Gemini detection
            gemini_result = await self._gemini_detect_domain(user_input)

            if gemini_result and gemini_result.get("confidence", 0) > confidence:
                self.logger.info(f"Gemini detection improved result: {gemini_result['primary_domain']} (confidence: {gemini_result['confidence']:.2f})")

                # Update analysis with Gemini result
                updated_analysis = preliminary_analysis.copy()
                updated_analysis.update({
                    "primary_domain": gemini_result["primary_domain"],
                    "confidence_score": gemini_result["confidence"],
                    "processing_method": "pattern_based_with_gemini_detection",
                    "gemini_detection_reason": reason,
                    "gemini_detection_improvement": gemini_result["confidence"] - confidence,
                    "original_pattern_domain": primary_domain,
                    "original_pattern_confidence": confidence
                })

                # Re-extract requirements and sub-domains for new domain
                new_requirements = self._extract_requirements(user_input, gemini_result["primary_domain"])
                new_sub_domains = self._extract_sub_domains(user_input, gemini_result["primary_domain"])
                new_success_metrics = self._identify_success_metrics(gemini_result["primary_domain"], new_requirements)
                new_stakeholders = self._identify_stakeholders(gemini_result["primary_domain"], new_requirements)

                updated_analysis.update({
                    "extracted_requirements": new_requirements,
                    "sub_domains": new_sub_domains,
                    "success_metrics": new_success_metrics,
                    "stakeholders": new_stakeholders
                })

                return updated_analysis
            else:
                self.logger.info("Gemini detection did not improve result, keeping pattern-based analysis")
                preliminary_analysis["gemini_detection_attempted"] = True
                preliminary_analysis["gemini_detection_reason"] = reason
                return preliminary_analysis

        except Exception as e:
            self.logger.error(f"Gemini detection failed: {e}")
            preliminary_analysis["gemini_detection_error"] = str(e)
            return preliminary_analysis

    async def _gemini_detect_domain(self, user_input: str) -> Dict[str, Any]:
        """
        Use Gemini to detect domain directly when pattern-based classification fails
        """
        try:
            # Get available domains
            available_domains = list(self.domain_patterns.keys())

            # Create domain detection prompt
            system_prompt = "You are a domain classification expert. Always respond with valid JSON."

            user_prompt = f"""
You are a Domain Classification Expert with deep expertise in technology project classification.

TASK: Analyze the following input and identify the most accurate domain from the available list.

AVAILABLE DOMAINS:
{', '.join(available_domains)}

INPUT TO ANALYZE:
"{user_input}"

ANALYSIS GUIDELINES:
1. Read the input carefully and understand user intent
2. Identify key keywords and important context
3. Compare with characteristics of each domain
4. Choose the most suitable domain
5. Assess confidence level

SPECIAL ATTENTION:
- fintech: Financial projects, banking, payments, investments
- ai_ml_development: AI applications, chatbots, computer vision, NLP
- data_science: Data analysis, analytics, reporting, visualization
- blockchain_development: Smart contracts, DeFi, NFT, Web3
- mobile_app_development: iOS/Android apps, cross-platform
- healthcare_tech: Medical systems, telemedicine, health apps
- e_commerce: Online stores, marketplaces, shopping platforms

RETURN EXACT JSON:
{{
    "primary_domain": "domain_name_from_list",
    "confidence": 0.85,
    "reasoning": "Detailed explanation in English",
    "key_indicators": ["indicator1", "indicator2", "indicator3"],
    "alternative_domains": ["domain2", "domain3"]
}}

IMPORTANT: primary_domain MUST be one of the domains available in the list above.
"""

            response = await self._call_gemini_direct(
                system_prompt=system_prompt,
                user_prompt=user_prompt,
                temperature=0.1,
                max_tokens=800
            )

            if not response or 'content' not in response:
                self.logger.warning("Gemini detection: No response received")
                return None

            # Parse Gemini response
            result = self._parse_gemini_response(response['content'], "domain_detection")

            if result and result.get("primary_domain") in available_domains:
                self.logger.info(f"Gemini detection successful: {result['primary_domain']} (confidence: {result.get('confidence', 0):.2f})")
                return result
            else:
                self.logger.warning(f"Gemini detection returned invalid domain: {result.get('primary_domain') if result else 'None'}")
                return None

        except Exception as e:
            self.logger.error(f"Gemini domain detection failed: {e}")
            return None

    def _validate_output(self, output: Dict[str, Any]) -> bool:
        """Validate domain analysis output"""
        analysis = output.get("domain_analysis", {})

        required_fields = [
            "primary_domain", "sub_domains", "complexity_level",
            "confidence_score", "extracted_requirements",
            "constraints", "success_metrics", "stakeholders"
        ]

        # Check required fields
        for field in required_fields:
            if field not in analysis:
                self.logger.error(f"Missing required field: {field}")
                return False

        # Validate data types and ranges
        if not isinstance(analysis["confidence_score"], (int, float)):
            self.logger.error("Confidence score must be numeric")
            return False

        if not (0.0 <= analysis["confidence_score"] <= 1.0):
            self.logger.error("Confidence score must be between 0 and 1")
            return False

        if not isinstance(analysis["sub_domains"], list):
            self.logger.error("Sub-domains must be a list")
            return False

        if not isinstance(analysis["extracted_requirements"], dict):
            self.logger.error("Requirements must be a dictionary")
            return False

        # Validate requirements structure
        req_types = ["functional", "non_functional", "technical"]
        for req_type in req_types:
            if req_type not in analysis["extracted_requirements"]:
                self.logger.error(f"Missing requirement type: {req_type}")
                return False

            if not isinstance(analysis["extracted_requirements"][req_type], list):
                self.logger.error(f"Requirement type {req_type} must be a list")
                return False

        return True
